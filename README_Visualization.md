# Bird Classification Training Visualization

Script untuk memvisualisasikan dan menyimpan hasil training CNN klasifikasi burung.

## 📁 File yang Dibuat

1. **`bird_training_visualization.py`** - <PERSON><PERSON>l utama untuk visualisasi
2. **`run_bird_visualization.py`** - Script untuk menjalankan visualisasi
3. **`run_visualization.bat`** - Batch file untuk Windows (double-click untuk run)
4. **`README_Visualization.md`** - Panduan ini

## 🚀 Cara Penggunaan

### Opsi 1: Double-click (Windows)
```
Double-click file: run_visualization.bat
```

### Opsi 2: Command Prompt
```bash
# Jalankan dengan sample data
python run_bird_visualization.py

# Jalankan dengan file history Anda sendiri
python run_bird_visualization.py path/to/your/history.pkl
```

### Opsi 3: Import dalam kode Python Anda
```python
from bird_training_visualization import visualize_and_save_training_results

# Setelah training model
history = model.fit(...)
test_loss, test_accuracy = model.evaluate(test_generator)

# Jalankan visualisasi
results = visualize_and_save_training_results(
    history=history,
    test_accuracy=test_accuracy,
    test_loss=test_loss,
    total_train_images=2820,
    total_test_images=1214
)
```

## 📊 Output yang Dihasilkan

Script akan membuat folder `trainResult/` dengan file-file berikut:

1. **`bird_classification_training_plot.png`** - Grafik training & validation
2. **`bird_classification_accuracy.csv`** - Data accuracy per epoch
3. **`bird_classification_loss.csv`** - Data loss per epoch  
4. **`bird_classification_training_summary.json`** - Ringkasan lengkap training

## 📋 Requirements

```bash
pip install matplotlib pandas numpy
```

## 🔧 Integrasi dengan Notebook Anda

Untuk mengintegrasikan dengan notebook bird classification Anda:

### 1. Tambahkan di akhir training:
```python
# Setelah model.fit()
from bird_training_visualization import visualize_and_save_training_results

# Jalankan visualisasi
results = visualize_and_save_training_results(
    history=history,
    test_accuracy=test_accuracy,
    test_loss=test_loss,
    total_train_images=total_train_images,
    total_test_images=total_test_images,
    results_dir="/content/trainResult"  # Untuk Colab
)
```

### 2. Atau simpan history untuk digunakan nanti:
```python
# Simpan history
import pickle
with open('training_history.pkl', 'wb') as f:
    pickle.dump(history, f)

# Kemudian jalankan script ini
```

## 📈 Fitur Visualisasi

- **Grafik Accuracy**: Training vs Validation accuracy per epoch
- **Grafik Loss**: Training vs Validation loss per epoch  
- **Data Export**: CSV files untuk analisis lebih lanjut
- **Summary Report**: JSON dengan semua metrics penting
- **Best Performance**: Otomatis mencari epoch dengan performa terbaik

## 🎯 Contoh Output

```
🐦 Bird Classification Training Visualization
============================================================
✅ History berhasil dimuat dari: training_history.pkl
🚀 Memulai visualisasi dan penyimpanan hasil training...
📊 Membuat visualisasi training...
📁 Direktori 'trainResult' berhasil dibuat.
💾 Plot disimpan ke: trainResult/bird_classification_training_plot.png
📋 Membuat DataFrame untuk accuracy dan loss...

💾 Hasil training berhasil disimpan:
   - Data accuracy: trainResult/bird_classification_accuracy.csv
   - Data loss: trainResult/bird_classification_loss.csv

📊 RINGKASAN TRAINING:
============================================================
Model: Bird Classification CNN
Dataset: Bird Classification Dataset (3 classes)
Classes: ['background', 'hama_burung', 'non_hama_burung']
Total Epochs: 20
Final Training Accuracy: 0.9234
Final Validation Accuracy: 0.8756
Best Validation Accuracy: 0.8834
Best Validation Accuracy Epoch: 18

✅ Semua hasil training berhasil disimpan dan divisualisasikan!
```

## 🛠️ Troubleshooting

### Error: Module not found
```bash
pip install matplotlib pandas numpy
```

### Error: Python tidak ditemukan
- Pastikan Python terinstall
- Tambahkan Python ke PATH environment variable

### Error: Permission denied
- Jalankan command prompt sebagai Administrator
- Atau ubah lokasi output folder

## 📞 Support

Jika ada masalah, pastikan:
1. Python terinstall dengan benar
2. Dependencies terinstall (`matplotlib`, `pandas`, `numpy`)
3. File history dalam format yang benar (.pkl, .pickle, atau .npy)
4. Memiliki permission untuk membuat folder dan file

---
**Dibuat untuk Bird Classification CNN Project** 🐦
