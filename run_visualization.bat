@echo off
echo ========================================
echo Bird Classification Training Visualization
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python tidak ditemukan!
    echo Pastikan Python sudah terinstall dan ada di PATH
    pause
    exit /b 1
)

echo Python ditemukan, melanjutkan...
echo.

REM Check if required packages are installed
echo Mengecek dependencies...
python -c "import matplotlib, pandas, numpy" >nul 2>&1
if errorlevel 1 (
    echo WARNING: Beberapa package mungkin belum terinstall
    echo Mencoba menginstall dependencies...
    pip install matplotlib pandas numpy
    if errorlevel 1 (
        echo ERROR: Gagal menginstall dependencies
        pause
        exit /b 1
    )
)

echo Dependencies OK!
echo.

REM Run the visualization script
echo Menjalankan visualisasi training...
echo.
python run_bird_visualization.py %1

echo.
echo ========================================
echo Proses selesai!
echo ========================================
pause
