#!/usr/bin/env python3
"""
Bird Classification Training Visualization and Results Saving
Script untuk visualisasi dan penyimpanan hasil training CNN klasifikasi burung
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

def visualize_and_save_training_results(history, test_accuracy=None, test_loss=None, 
                                       total_train_images=None, total_test_images=None,
                                       results_dir="./trainResult"):
    """
    Fungsi untuk memvisualisasikan dan menyimpan hasil training
    
    Parameters:
    - history: Training history dari model.fit()
    - test_accuracy: Akurasi pada test set (optional)
    - test_loss: Loss pada test set (optional)
    - total_train_images: Total gambar training (optional)
    - total_test_images: Total gambar testing (optional)
    - results_dir: Direktori untuk menyimpan hasil
    """
    
    print("🚀 Memulai visualisasi dan penyimpanan hasil training...")
    
    # Extract training history
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    
    epochs = range(len(acc))
    
    # Create plots
    print("📊 Membuat visualisasi training...")
    plt.figure(figsize=(15, 5))
    
    # Plot Training and Validation Accuracy
    plt.subplot(1, 2, 1)
    plt.plot(epochs, acc, 'b-', linewidth=2, label='Training Accuracy')
    plt.plot(epochs, val_acc, 'r-', linewidth=2, label='Validation Accuracy')
    plt.title('Training and Validation Accuracy', fontsize=14, fontweight='bold')
    plt.ylabel('Accuracy', fontsize=12)
    plt.xlabel('Epoch', fontsize=12)
    plt.legend(['train', 'val'], loc='upper left')
    plt.grid(True, alpha=0.3)
    
    # Plot Training and Validation Loss
    plt.subplot(1, 2, 2)
    plt.plot(epochs, loss, 'b-', linewidth=2, label='Training Loss')
    plt.plot(epochs, val_loss, 'r-', linewidth=2, label='Validation Loss')
    plt.title('Training and Validation Loss', fontsize=14, fontweight='bold')
    plt.ylabel('Loss', fontsize=12)
    plt.xlabel('Epoch', fontsize=12)
    plt.legend(['train', 'val'], loc='upper left')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Create results directory
    if not os.path.exists(results_dir):
        os.mkdir(results_dir)  # Menggunakan os.mkdir seperti yang diminta
        print(f"📁 Direktori '{results_dir}' berhasil dibuat.")
    else:
        print(f"📁 Direktori '{results_dir}' sudah ada.")
    
    # Save plot
    plot_path = os.path.join(results_dir, "bird_classification_training_plot.png")
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"💾 Plot disimpan ke: {plot_path}")
    plt.show()
    
    # Create DataFrames
    print("📋 Membuat DataFrame untuk accuracy dan loss...")
    accuracy_df = pd.DataFrame.from_dict(history.history)
    loss_df = pd.DataFrame.from_dict(history.history)
    
    print("\nAccuracy DataFrame:")
    print("=" * 50)
    print(accuracy_df[["accuracy", "val_accuracy"]])
    
    print("\nLoss DataFrame:")
    print("=" * 50)
    print(loss_df[["loss", "val_loss"]])
    
    # Save CSV files
    accuracy_csv_path = os.path.join(results_dir, "bird_classification_accuracy.csv")
    loss_csv_path = os.path.join(results_dir, "bird_classification_loss.csv")
    
    accuracy_df[["accuracy", "val_accuracy"]].to_csv(accuracy_csv_path, index=True)
    loss_df[["loss", "val_loss"]].to_csv(loss_csv_path, index=True)
    
    print(f"\n💾 Hasil training berhasil disimpan:")
    print(f"   - Data accuracy: {accuracy_csv_path}")
    print(f"   - Data loss: {loss_csv_path}")
    
    # Create training summary
    training_summary = {
        'Model': 'Bird Classification CNN',
        'Dataset': 'Bird Classification Dataset (3 classes)',
        'Classes': ['background', 'hama_burung', 'non_hama_burung'],
        'Training_Date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'Total_Epochs': len(acc),
        'Final_Training_Accuracy': f"{acc[-1]:.4f}",
        'Final_Validation_Accuracy': f"{val_acc[-1]:.4f}",
        'Final_Training_Loss': f"{loss[-1]:.4f}",
        'Final_Validation_Loss': f"{val_loss[-1]:.4f}",
        'Best_Validation_Accuracy': f"{max(val_acc):.4f}",
        'Best_Validation_Accuracy_Epoch': val_acc.index(max(val_acc)) + 1,
        'Best_Training_Accuracy': f"{max(acc):.4f}",
        'Best_Training_Accuracy_Epoch': acc.index(max(acc)) + 1
    }
    
    # Add optional parameters if provided
    if total_train_images is not None:
        training_summary['Total_Training_Images'] = total_train_images
    if total_test_images is not None:
        training_summary['Total_Testing_Images'] = total_test_images
    if test_accuracy is not None:
        training_summary['Test_Accuracy'] = f"{test_accuracy:.4f}"
    if test_loss is not None:
        training_summary['Test_Loss'] = f"{test_loss:.4f}"
    
    # Save summary to JSON
    summary_path = os.path.join(results_dir, "bird_classification_training_summary.json")
    with open(summary_path, 'w') as f:
        json.dump(training_summary, f, indent=4)
    
    print("\n📊 RINGKASAN TRAINING:")
    print("=" * 60)
    for key, value in training_summary.items():
        print(f"{key.replace('_', ' ').title()}: {value}")
    
    print(f"\n💾 Ringkasan training disimpan ke: {summary_path}")
    
    # Display CSV previews
    print("\n" + "=" * 60)
    print("PREVIEW ACCURACY CSV:")
    print("=" * 60)
    print(pd.read_csv(accuracy_csv_path).head())
    
    print("\n" + "=" * 60)
    print("PREVIEW LOSS CSV:")
    print("=" * 60)
    print(pd.read_csv(loss_csv_path).head())
    
    print("\n✅ Semua hasil training berhasil disimpan dan divisualisasikan!")
    
    return {
        'accuracy_csv': accuracy_csv_path,
        'loss_csv': loss_csv_path,
        'summary_json': summary_path,
        'plot_image': plot_path
    }

def main():
    """
    Contoh penggunaan script ini
    """
    print("🔧 Script Bird Classification Training Visualization")
    print("=" * 60)
    print("Untuk menggunakan script ini, import fungsi visualize_and_save_training_results")
    print("dan panggil dengan parameter history dari training model Anda.")
    print("\nContoh penggunaan:")
    print("from bird_training_visualization import visualize_and_save_training_results")
    print("results = visualize_and_save_training_results(history, test_accuracy, test_loss)")
    print("=" * 60)

if __name__ == "__main__":
    main()
