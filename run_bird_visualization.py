#!/usr/bin/env python3
"""
Script untuk menjalankan visualisasi hasil training Bird Classification
Jalankan dengan: python run_bird_visualization.py
"""

import sys
import os
import pickle
import numpy as np
from bird_training_visualization import visualize_and_save_training_results

def load_training_history(history_file_path):
    """
    Load training history dari file pickle atau numpy
    """
    try:
        if history_file_path.endswith('.pkl') or history_file_path.endswith('.pickle'):
            with open(history_file_path, 'rb') as f:
                history = pickle.load(f)
        elif history_file_path.endswith('.npy'):
            history_data = np.load(history_file_path, allow_pickle=True).item()
            # Create mock history object
            class MockHistory:
                def __init__(self, history_dict):
                    self.history = history_dict
            history = MockHistory(history_data)
        else:
            print("❌ Format file tidak didukung. Gunakan .pkl, .pickle, atau .npy")
            return None
        
        print(f"✅ History berhasil dimuat dari: {history_file_path}")
        return history
    except Exception as e:
        print(f"❌ Error loading history: {e}")
        return None

def create_sample_history():
    """
    Membuat sample history untuk testing (jika tidak ada file history)
    """
    print("🔧 Membuat sample training history untuk demonstrasi...")
    
    # Sample data yang mirip dengan hasil training sesungguhnya
    epochs = 20
    
    # Simulasi training yang realistis
    base_acc = 0.3
    base_val_acc = 0.25
    base_loss = 1.5
    base_val_loss = 1.6
    
    accuracy = []
    val_accuracy = []
    loss = []
    val_loss = []
    
    for i in range(epochs):
        # Simulasi peningkatan accuracy dan penurunan loss
        acc_improvement = (0.95 - base_acc) * (1 - np.exp(-i/5)) + np.random.normal(0, 0.02)
        val_acc_improvement = (0.88 - base_val_acc) * (1 - np.exp(-i/6)) + np.random.normal(0, 0.03)
        
        loss_decrease = base_loss * np.exp(-i/8) + np.random.normal(0, 0.05)
        val_loss_decrease = base_val_loss * np.exp(-i/7) + np.random.normal(0, 0.06)
        
        accuracy.append(min(0.98, base_acc + acc_improvement))
        val_accuracy.append(min(0.92, base_val_acc + val_acc_improvement))
        loss.append(max(0.05, loss_decrease))
        val_loss.append(max(0.08, val_loss_decrease))
    
    # Create mock history object
    class MockHistory:
        def __init__(self):
            self.history = {
                'accuracy': accuracy,
                'val_accuracy': val_accuracy,
                'loss': loss,
                'val_loss': val_loss
            }
    
    return MockHistory()

def main():
    """
    Main function untuk menjalankan visualisasi
    """
    print("🐦 Bird Classification Training Visualization")
    print("=" * 60)
    
    # Check if history file is provided as argument
    if len(sys.argv) > 1:
        history_file = sys.argv[1]
        if os.path.exists(history_file):
            history = load_training_history(history_file)
            if history is None:
                return
        else:
            print(f"❌ File tidak ditemukan: {history_file}")
            return
    else:
        print("ℹ️  Tidak ada file history yang diberikan.")
        print("💡 Menggunakan sample data untuk demonstrasi...")
        print("   Untuk menggunakan data sesungguhnya, jalankan:")
        print("   python run_bird_visualization.py path/to/your/history.pkl")
        print()
        
        response = input("Lanjutkan dengan sample data? (y/n): ").lower().strip()
        if response != 'y' and response != 'yes':
            print("❌ Dibatalkan oleh user.")
            return
        
        history = create_sample_history()
    
    # Sample additional data (sesuaikan dengan data Anda)
    sample_data = {
        'test_accuracy': 0.8542,
        'test_loss': 0.3421,
        'total_train_images': 2820,
        'total_test_images': 1214
    }
    
    print("\n🚀 Memulai proses visualisasi...")
    
    # Jalankan visualisasi
    try:
        results = visualize_and_save_training_results(
            history=history,
            test_accuracy=sample_data['test_accuracy'],
            test_loss=sample_data['test_loss'],
            total_train_images=sample_data['total_train_images'],
            total_test_images=sample_data['total_test_images'],
            results_dir="./trainResult"
        )
        
        print("\n🎉 PROSES SELESAI!")
        print("=" * 60)
        print("File yang telah dibuat:")
        for key, path in results.items():
            print(f"  📄 {key}: {path}")
        
        print("\n💡 Tips:")
        print("  - Buka file PNG untuk melihat grafik training")
        print("  - Buka file CSV untuk analisis data lebih lanjut")
        print("  - Buka file JSON untuk ringkasan lengkap")
        
    except Exception as e:
        print(f"❌ Error saat menjalankan visualisasi: {e}")
        print("🔧 Pastikan semua dependencies terinstall:")
        print("   pip install matplotlib pandas numpy")

if __name__ == "__main__":
    main()
